<script setup lang="ts">
import { formatNumber } from '~/utils'
import {
  compareImageArrays,
  commonValidationRules
} from '~/utils/generationValidation'

interface ImageFile {
  src: string
  alt: string
  file: File
}
const { authorize } = useAuthorize()
const authStore = useAuthStore()
const { isAuthenticated, user_credit } = storeToRefs(authStore)
const { model, models } = useVideoGenModels()
const {
  duration,
  durationOptions,
  isDurationSelectable,
  personGeneration,
  personGenerationOptions,
  enhancePrompt,
  isEnhancePromptLocked
} = useVideoGenOptions()
const { videoDimension } = useVideoDimensions()
const videoStyle = ref('Cinematic')
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()
const textToVideoStore = useTextToVideoStore()
const { textToVideoResult, aiToolVideoCardRef, prompt, loadings, errors }
  = storeToRefs(textToVideoStore)
const productStore = useProductStore()
const { getServicePriceByModelName } = storeToRefs(productStore)
// Local state for selected images
const selectedImages = ref<ImageFile[]>([])

// Store initial values to compare for changes
const initialValues = ref({
  prompt: '',
  model: models[0],
  videoDimension: '16:9',
  videoStyle: 'Cinematic',
  enhancePrompt: false,
  selectedImages: [] as ImageFile[]
})

// Initialize initial values on mount
onMounted(() => {
  initialValues.value = {
    prompt: prompt.value,
    model: model.value,
    videoDimension: videoDimension.value,
    videoStyle: videoStyle.value,
    enhancePrompt: enhancePrompt.value,
    selectedImages: [...selectedImages.value]
  }
})

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged
    = prompt.value !== initialValues.value.prompt
      || model.value?.value !== initialValues.value.model?.value
      || videoDimension.value !== initialValues.value.videoDimension
      || videoStyle.value !== initialValues.value.videoStyle
      || enhancePrompt.value !== initialValues.value.enhancePrompt

  // Image comparison with better performance
  const imagesChanged = compareImageArrays(
    selectedImages.value,
    initialValues.value.selectedImages
  )

  return basicFieldsChanged || imagesChanged
})

// Handle image selection
const handleImagesSelected = (images: ImageFile[]) => {
  selectedImages.value = images
  // Also update store for backward compatibility
  textToVideoStore.selectedImages = images
}

// Helper function to perform the actual generation
const performGeneration = async () => {
  // Extract File objects from selected images
  const files = selectedImages.value.map(img => img.file).filter(Boolean)

  const result = await textToVideoStore.textToVideo({
    prompt: prompt.value,
    model: model.value?.value || 'veo-2',
    aspect_ratio: videoDimension.value || '16:9',
    enhance_prompt: enhancePrompt.value,
    duration: duration.value,
    files: files
  })

  if (result) {
    const generationType
      = selectedImages.value.length > 0 ? 'Image-to-Video' : 'Text-to-Video'
    toast.add({
      id: 'success',
      title: `${generationType} Generated`,
      description: 'Your video is being generated. Please check back later.',
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      prompt: prompt.value,
      model: model.value,
      videoDimension: videoDimension.value,
      videoStyle: videoStyle.value,
      enhancePrompt: enhancePrompt.value,
      selectedImages: [...selectedImages.value]
    }
  }
}

const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Define validation rules
  const validationRules = [
    commonValidationRules.requiredText(
      prompt.value,
      t('Please enter a prompt to generate a video.')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'video',
    hasChanges,
    hasResult: computed(() => !!textToVideoResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}

const onUsePrompt = (newPrompt: string) => {
  prompt.value = newPrompt
  // scroll to top and focus on prompt input
  nextTick(() => {
    // scroll to top smoothly
    window.scrollTo({ top: 0, behavior: 'smooth' })

    // try to focus the prompt input after scrolling
    setTimeout(() => {
      // look for the prompt input (UChatPrompt component)
      const promptInput = document.querySelector(
        '[class*="chat-prompt"] textarea, [class*="chat-prompt"] input'
      )
      if (promptInput && promptInput instanceof HTMLElement) {
        promptInput.focus()
      }
    }, 500)
  })
}

const enhancePromptItems = computed(() => {
  const items = [
    {
      label: t('On'),
      value: true,
      description: isEnhancePromptLocked.value
        ? t('Prompts will always be refined to improve output quality (required for this model)')
        : t('Prompts will always be refined to improve output quality'),
      disabled: isEnhancePromptLocked.value && !enhancePrompt.value
    },
    {
      label: t('Off'),
      value: false,
      description: t('Prompts will not be modified'),
      disabled: isEnhancePromptLocked.value && enhancePrompt.value
    }
  ]

  return items
})
</script>

<template>
  <UContainer class="mt-0">
    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField :label="$t('model')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                class="w-full"
              />
            </UFormField>
            <div
              v-if="model?.options?.includes('yourImage')"
              class="flex flex-row gap-3 items-end"
            >
              <UFormField :label="$t('Image Reference')">
                <BaseImageSelect
                  v-model="selectedImages"
                  :multiple="true"
                  @update:model-value="handleImagesSelected"
                />
              </UFormField>
              <BaseImageSelectedList
                v-model="selectedImages"
                @update:model-value="handleImagesSelected"
              />
            </div>
          </div>
          <UFormField :label="$t('Prompt')">
            <UTextarea
              v-model="prompt"
              class="w-full"
              :placeholder="$t('Describe the video you want to generate...')"
              :rows="6"
            />
          </UFormField>

          <div class="space-y-6">
            <!-- First row: Enhance Prompt and Aspect Ratio -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UFormField :label="$t('enhancePrompt')">
                <template #hint>
                  <UTooltip
                    :delay-duration="0"
                    :text="
                      isEnhancePromptLocked
                        ? $t('This setting is locked for the selected model')
                        : enhancePrompt
                          ? $t('Prompts will always be refined to improve output quality')
                          : $t('Prompts will not be refined')
                    "
                  >
                    <UIcon name="material-symbols:help" />
                  </UTooltip>
                </template>
                <URadioGroup
                  v-model="enhancePrompt"
                  orientation="horizontal"
                  variant="card"
                  value-key="value"
                  :items="enhancePromptItems"
                  size="xs"
                  :disabled="isEnhancePromptLocked"
                />
              </UFormField>

              <UFormField :label="$t('aspectRatio')">
                <BaseVideoDimensionsSelect :options="model?.ratios" />
              </UFormField>
            </div>

            <!-- Second row: Duration (only if selectable) -->
            <UFormField
              v-if="isDurationSelectable"
              :label="$t('Duration')"
            >
              <template #hint>
                <UTooltip
                  :delay-duration="0"
                  :text="$t('Select video duration in seconds')"
                >
                  <UIcon name="material-symbols:help" />
                </UTooltip>
              </template>
              <URadioGroup
                v-model="duration"
                orientation="horizontal"
                variant="card"
                value-key="value"
                :items="durationOptions"
                size="xs"
              />
            </UFormField>
          </div>

          <div class="flex justify-end gap-2 items-center flex-row">
            <div class="text-xs text-right">
              <div>
                {{
                  $t("Credits: {credits} remaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary">
                {{
                  $t("This generation will cost: {cost} Credits", {
                    cost:
                      getServicePriceByModelName(model?.value)
                        ?.effective_price || 0
                  })
                }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="$t('Generate Video')"
              class="bg-gradient-to-r from-primary-500 to-primary-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-success-600 cursor-pointer"
              trailing-icon="line-md:arrow-right"
              :loading="loadings['textToVideo']"
              :disabled="!prompt"
              @click="authorize(onGenerate)"
            />
          </div>
        </div>
      </UCard>
      <Motion
        v-if="
          (textToVideoResult || loadings['textToVideo'])
            && !errors['textToVideo']
        "
        ref="aiToolVideoCardRef"
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolVideoCard
          v-bind="textToVideoResult"
          :data="textToVideoResult"
          :loading="loadings['textToVideo']"
          class="h-full"
        />
      </Motion>
      <UCard
        v-else
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <div>
            <UIcon
              :name="
                errors['textToVideo']
                  ? 'i-lucide-alert-circle'
                  : 'i-lucide-video'
              "
              class="text-6xl mb-2"
              :class="errors['textToVideo'] ? 'text-error' : ''"
            />
          </div>
          <div
            v-if="errors['textToVideo']"
            class="text-sm text-error"
          >
            {{ $t(errors["textToVideo"] || "Something went wrong") }}
          </div>
          <div
            v-else
            class="text-sm"
          >
            {{ $t("Your generated video will appear here") }}
          </div>
        </div>
      </UCard>
    </div>
    <!-- Video Prompt Gallery -->
    <Motion
      :initial="{
        scale: 1.1,
        opacity: 0,
        filter: 'blur(20px)'
      }"
      :animate="{
        scale: 1,
        opacity: 1,
        filter: 'blur(0px)'
      }"
      :transition="{
        duration: 0.6,
        delay: 1.2
      }"
    >
      <VideoPromptGallery
        class="mt-8"
        @use-prompt="onUsePrompt"
      />
    </Motion>
  </UContainer>
</template>
